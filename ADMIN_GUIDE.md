# 🔧 Admin Guide - API Monitor Premium System

## 📋 Overview

Sistem monetisasi manual untuk API Monitor menggunakan PayPal dan Telegram bot untuk konfirmasi pembayaran.

## 🗂️ File Structure

```
/project-folder/
├── premium.json          ← Daftar pengguna premium
├── usage.json            ← Tracking penggunaan harian
├── public/upgrade.html   ← Halaman upgrade premium
└── .env                  ← Konfigurasi PayPal & Telegram
```

## 💎 Mengelola Pengguna Premium

### 1. Format File `premium.json`

```json
{
  "premium_users": [
    {
      "chat_id": "123456789",
      "ip_address": "*************",
      "payment_method": "paypal",
      "payment_id": "PAYPAL-TXN-123456",
      "activated_date": "2024-01-15T10:30:00Z",
      "expires_date": "2025-01-15T10:30:00Z",
      "status": "active",
      "notes": "Manual verification - PayPal payment confirmed"
    }
  ],
  "settings": {
    "premium_price_usd": 5,
    "premium_duration_days": 365,
    "free_daily_limit": 3,
    "premium_daily_limit": 1000
  }
}
```

### 2. Menambah Pengguna Premium Baru

Ketika menerima bukti pembayaran via Telegram:

1. **Verifikasi pembayaran** di PayPal dashboard
2. **Dapatkan IP address** dari user (mereka harus kirim IP address)
3. **Tambahkan entry baru** ke `premium.json`:

```json
{
  "chat_id": "USER_CHAT_ID",
  "ip_address": "USER_IP_ADDRESS",
  "payment_method": "paypal",
  "payment_id": "PAYPAL_TRANSACTION_ID",
  "activated_date": "2024-01-15T10:30:00Z",
  "expires_date": "2025-01-15T10:30:00Z",
  "status": "active",
  "notes": "Manual verification - PayPal payment confirmed"
}
```

### 3. Menonaktifkan Pengguna Premium

Ubah `status` menjadi `"inactive"`:

```json
{
  "status": "inactive",
  "notes": "Deactivated - reason here"
}
```

## 📊 Monitoring Usage

### File `usage.json`

Sistem otomatis tracking penggunaan harian:

```json
{
  "daily_usage": {
    "2024-01-15": {
      "*************": {
        "count": 2,
        "first_request": "2024-01-15T08:30:00Z",
        "last_request": "2024-01-15T10:15:00Z",
        "requests": [...]
      }
    }
  }
}
```

### Cleanup Otomatis

- Data usage otomatis dihapus setelah 7 hari
- Tidak perlu maintenance manual

## 🔧 Konfigurasi

### File `.env`

```env
# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# Monetization Configuration
PAYPAL_USERNAME=your_paypal_username
TELEGRAM_BOT_USERNAME=your_bot_username
PREMIUM_PRICE=5
FREE_DAILY_LIMIT=3
```

### Update Link PayPal & Telegram

Edit file `public/upgrade.html` dan ganti:
- `NAMA_PAYPAL` dengan username PayPal Anda
- `NAMA_BOT_TELEGRAM` dengan username bot Telegram Anda

## 🚀 API Endpoints

### 1. Cek Status User

```bash
GET /status?chat_id=123456789
```

Response:
```json
{
  "ip_address": "*************",
  "chat_id": "123456789",
  "isPremium": true,
  "used": 5,
  "remaining": "unlimited",
  "limit": 3,
  "allowed": true
}
```

### 2. Monitor API

```bash
GET /check?url=https://api.example.com&chat_id=123456789
```

Response untuk user premium:
```json
{
  "ok": true,
  "status": 200,
  "usage_info": {
    "isPremium": true,
    "used": "unlimited",
    "remaining": "unlimited"
  }
}
```

Response untuk user gratis (limit tercapai):
```json
{
  "error": "limit_exceeded",
  "message": "Batas harian tercapai. Upgrade ke premium untuk akses unlimited.",
  "used": 3,
  "limit": 3,
  "remaining": 0,
  "upgrade_url": "/upgrade.html",
  "isPremium": false
}
```

## 📱 Workflow Pembayaran

### 1. User Flow

1. User mencapai limit gratis (3x/hari)
2. Redirect otomatis ke `/upgrade.html`
3. User klik "Bayar via PayPal" → bayar $5
4. User screenshot bukti pembayaran
5. User klik "Kirim Bukti Bayar" → buka Telegram
6. User kirim screenshot + IP address ke bot

### 2. Admin Flow

1. Terima notifikasi pembayaran di Telegram
2. Verifikasi pembayaran di PayPal dashboard
3. Tambah user ke `premium.json`
4. Konfirmasi ke user via Telegram
5. User refresh halaman → akses unlimited aktif

## 🛠️ Troubleshooting

### User Tidak Bisa Akses Premium

1. **Cek file `premium.json`**:
   - Pastikan `status: "active"`
   - Pastikan `expires_date` belum lewat
   - Pastikan `ip_address` atau `chat_id` sesuai

2. **Restart server** setelah edit `premium.json`

### Limit Tidak Reset Harian

- Sistem otomatis reset berdasarkan tanggal
- Tidak perlu action manual

### Error File Permission

```bash
# Fix permission di Linux/Mac
chmod 644 premium.json usage.json

# Pastikan server bisa write ke usage.json
chmod 666 usage.json
```

## 📈 Analytics

### Melihat Usage Statistics

```bash
# Total user hari ini
cat usage.json | grep "$(date +%Y-%m-%d)" | wc -l

# Total premium users
cat premium.json | grep '"status": "active"' | wc -l
```

### Revenue Tracking

- Manual tracking via PayPal dashboard
- Setiap entry di `premium.json` = $5 revenue
- Bisa buat script untuk hitung total revenue

## 🔒 Security Notes

1. **Jangan commit** file `.env` ke git
2. **Backup** `premium.json` secara berkala
3. **Monitor** file `usage.json` untuk abuse
4. **Validasi** semua pembayaran PayPal
5. **Rate limiting** sudah built-in per IP

## 📞 Support

Untuk pertanyaan teknis atau masalah sistem, cek:
1. Server logs: `node server.js`
2. File permissions
3. Format JSON yang valid
4. Konfigurasi `.env`

---

**Happy Monetizing! 💰**
