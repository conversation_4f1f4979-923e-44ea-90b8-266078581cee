<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upgrade to Premium - API Monitor</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            padding: 50px;
            max-width: 700px;
            width: 100%;
            text-align: center;
        }
        
        .limit-icon {
            font-size: 4em;
            margin-bottom: 20px;
            color: #ff6b6b;
        }
        
        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 15px;
        }
        
        .header p {
            color: #666;
            font-size: 1.2em;
            margin-bottom: 30px;
        }
        
        .limit-info {
            background: #fff5f5;
            border: 2px solid #fed7d7;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 40px;
        }
        
        .limit-info h3 {
            color: #c53030;
            font-size: 1.4em;
            margin-bottom: 10px;
        }
        
        .limit-info p {
            color: #744210;
            font-size: 1.1em;
        }
        
        .features {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .feature-box {
            text-align: left;
        }
        
        .feature-box h4 {
            color: #333;
            font-size: 1.3em;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .feature-box h4 span {
            margin-right: 10px;
            font-size: 1.2em;
        }
        
        .free-features {
            background: #f7fafc;
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            padding: 20px;
        }
        
        .premium-features {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }
        
        .premium-features::before {
            content: "⭐ PREMIUM";
            position: absolute;
            top: 10px;
            right: 15px;
            background: rgba(255,255,255,0.2);
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            margin-bottom: 10px;
            padding-left: 25px;
            position: relative;
        }
        
        .free-features .feature-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #48bb78;
            font-weight: bold;
        }
        
        .premium-features .feature-list li:before {
            content: "⭐";
            position: absolute;
            left: 0;
            color: #ffd700;
        }
        
        .pricing {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
        }
        
        .pricing h3 {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .price {
            font-size: 3em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .price-note {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .payment-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-bottom: 30px;
        }
        
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 1.1em;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            transition: transform 0.2s ease;
            cursor: pointer;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-paypal {
            background: #0070ba;
            color: white;
        }
        
        .btn-telegram {
            background: #0088cc;
            color: white;
        }
        
        .instructions {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            padding: 25px;
            text-align: left;
            margin-bottom: 30px;
        }
        
        .instructions h4 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .instructions ol {
            color: #6c757d;
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
            line-height: 1.5;
        }
        
        .back-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 768px) {
            .features {
                grid-template-columns: 1fr;
            }
            
            .payment-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .container {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="limit-icon">🚫</div>
        
        <div class="header">
            <h1>Limit Tercapai!</h1>
            <p>Anda telah mencapai batas gratis 3 pengecekan per hari</p>
        </div>
        
        <div class="limit-info">
            <h3>⏰ Batas Harian Terlampaui</h3>
            <p>Akun gratis dibatasi 3 pengecekan API per hari. Upgrade ke Premium untuk akses unlimited!</p>
        </div>
        
        <div class="features">
            <div class="feature-box">
                <div class="free-features">
                    <h4><span>🆓</span>Paket Gratis</h4>
                    <ul class="feature-list">
                        <li>3 pengecekan per hari</li>
                        <li>Alert Telegram basic</li>
                        <li>Interface web sederhana</li>
                        <li>Support komunitas</li>
                    </ul>
                </div>
            </div>
            
            <div class="feature-box">
                <div class="premium-features">
                    <h4><span>⭐</span>Paket Premium</h4>
                    <ul class="feature-list">
                        <li>Unlimited pengecekan</li>
                        <li>Alert Telegram prioritas</li>
                        <li>Monitoring otomatis</li>
                        <li>Support prioritas 24/7</li>
                        <li>API key personal</li>
                        <li>Custom interval checking</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="pricing">
            <h3>💎 Upgrade ke Premium</h3>
            <div class="price">$5 USD</div>
            <div class="price-note">Akses selamanya • Tanpa langganan bulanan</div>
        </div>
        
        <div class="payment-buttons">
            <a href="https://paypal.me/NAMA_PAYPAL/5" class="btn btn-paypal" target="_blank">
                💳 Bayar via PayPal
            </a>
            <a href="https://t.me/NAMA_BOT_TELEGRAM" class="btn btn-telegram" target="_blank">
                📱 Kirim Bukti Bayar
            </a>
        </div>
        
        <div class="instructions">
            <h4>📋 Cara Upgrade ke Premium:</h4>
            <ol>
                <li><strong>Klik "Bayar via PayPal"</strong> dan selesaikan pembayaran $5 USD</li>
                <li><strong>Screenshot bukti pembayaran</strong> dari PayPal (termasuk Transaction ID)</li>
                <li><strong>Klik "Kirim Bukti Bayar"</strong> untuk membuka chat Telegram</li>
                <li><strong>Kirim screenshot + IP address Anda:</strong> <code id="user-ip">Loading...</code></li>
                <li><strong>Tunggu konfirmasi</strong> dari admin (biasanya dalam 1-24 jam)</li>
                <li><strong>Akun Premium aktif!</strong> Refresh halaman dan nikmati akses unlimited</li>
            </ol>
        </div>
        
        <div style="margin-top: 30px;">
            <a href="/" class="back-link">
                ← Kembali ke Halaman Utama
            </a>
        </div>
    </div>

    <script>
        // Get user IP address for easier identification
        fetch('https://api.ipify.org?format=json')
            .then(response => response.json())
            .then(data => {
                document.getElementById('user-ip').textContent = data.ip;
            })
            .catch(error => {
                document.getElementById('user-ip').textContent = 'Tidak dapat mendeteksi IP';
            });
        
        // Update PayPal and Telegram links from URL parameters if provided
        const urlParams = new URLSearchParams(window.location.search);
        const paypalName = urlParams.get('paypal') || 'NAMA_PAYPAL';
        const telegramBot = urlParams.get('telegram') || 'NAMA_BOT_TELEGRAM';
        
        // Update links
        document.querySelector('.btn-paypal').href = `https://paypal.me/${paypalName}/5`;
        document.querySelector('.btn-telegram').href = `https://t.me/${telegramBot}`;
    </script>
</body>
</html>
