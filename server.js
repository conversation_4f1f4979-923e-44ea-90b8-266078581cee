const http = require('http');
const https = require('https');
const fs = require('fs');
const path = require('path');
const url = require('url');
const querystring = require('querystring');
const fetch = require('node-fetch');
require('dotenv').config();

const PORT = 3000;


// Fungsi untuk mengirim pesan ke Telegram
async function sendTelegramAlert(message) {
    const token = process.env.TELEGRAM_BOT_TOKEN;
    const chatId = process.env.TELEGRAM_CHAT_ID;
    
    if (!token || !chatId) {
        console.error('Telegram bot token atau chat ID tidak ditemukan di .env');
        return;
    }
    
    const telegramUrl = `https://api.telegram.org/bot${token}/sendMessage`;
    const payload = JSON.stringify({
        chat_id: chatId,
        text: message
    });
    
    try {
        const response = await fetch(telegramUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: payload
        });
        
        if (response.ok) {
            console.log('Pesan Telegram berhasil dikirim');
        } else {
            console.error('Gagal mengirim pesan Telegram:', response.statusText);
        }
    } catch (error) {
        console.error('Error mengirim pesan Telegram:', error.message);
    }
}

// Fungsi untuk melakukan pengecekan API
async function checkApiEndpoint(apiUrl) {
    try {
        const response = await fetch(apiUrl);
        const status = response.status;
        
        if (status === 200) {
            return { ok: true, status: 200 };
        } else {
            // Kirim alert ke Telegram jika status bukan 200
            const alertMessage = `🚨 API Alert!\nURL: ${apiUrl}\nStatus: ${status}\nTime: ${new Date().toISOString()}`;
            await sendTelegramAlert(alertMessage);
            
            return { ok: false, status: status };
        }
    } catch (error) {
        // Kirim alert ke Telegram jika terjadi error
        const alertMessage = `🚨 API Error!\nURL: ${apiUrl}\nError: ${error.message}\nTime: ${new Date().toISOString()}`;
        await sendTelegramAlert(alertMessage);
        
        return { ok: false, status: 'error', message: error.message };
    }
}

// Fungsi untuk melayani file statis
function serveStaticFile(filePath, res) {
    fs.readFile(filePath, (err, data) => {
        if (err) {
            res.writeHead(404, { 'Content-Type': 'text/plain' });
            res.end('File not found');
            return;
        }
        
        const ext = path.extname(filePath);
        let contentType = 'text/plain';
        
        switch (ext) {
            case '.html':
                contentType = 'text/html';
                break;
            case '.css':
                contentType = 'text/css';
                break;
            case '.js':
                contentType = 'application/javascript';
                break;
            case '.json':
                contentType = 'application/json';
                break;
        }
        
        res.writeHead(200, { 'Content-Type': contentType });
        res.end(data);
    });
}

// Membuat server HTTP
const server = http.createServer(async (req, res) => {
    const parsedUrl = url.parse(req.url, true);
    const pathname = parsedUrl.pathname;
    const query = parsedUrl.query;
    
    // CORS headers untuk development
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }
    
    // Route untuk halaman utama
    if (pathname === '/' && req.method === 'GET') {
        serveStaticFile(path.join(__dirname, 'public', 'index.html'), res);
        return;
    }
    
    // Route untuk pengecekan API
    if (pathname === '/check' && req.method === 'GET') {
        const apiUrl = query.url;
        
        if (!apiUrl) {
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Parameter URL diperlukan' }));
            return;
        }
        
        try {
            const result = await checkApiEndpoint(apiUrl);
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify(result));
        } catch (error) {
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Internal server error' }));
        }
        return;
    }
    
    // Route untuk melayani file statis dari folder public
    if (pathname.startsWith('/public/')) {
        const filePath = path.join(__dirname, pathname);
        serveStaticFile(filePath, res);
        return;
    }
    
    // 404 untuk route yang tidak ditemukan
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Route not found');
});

// Menjalankan server
server.listen(PORT, () => {
    console.log(`🚀 Server berjalan di http://localhost:${PORT}`);
    console.log(`📊 API Monitor + Alert siap digunakan!`);
});
