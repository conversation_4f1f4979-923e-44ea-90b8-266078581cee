const http = require('http');
const https = require('https');
const fs = require('fs');
const path = require('path');
const url = require('url');
const querystring = require('querystring');
const fetch = require('node-fetch');
require('dotenv').config();

const PORT = 3000;

// Fungsi untuk membaca file JSON dengan error handling
function readJsonFile(filePath, defaultData = {}) {
    try {
        if (fs.existsSync(filePath)) {
            const data = fs.readFileSync(filePath, 'utf8');
            return JSON.parse(data);
        }
        return defaultData;
    } catch (error) {
        console.error(`Error reading ${filePath}:`, error.message);
        return defaultData;
    }
}

// Fungsi untuk menulis file JSON dengan error handling
function writeJsonFile(filePath, data) {
    try {
        fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
        return true;
    } catch (error) {
        console.error(`Error writing ${filePath}:`, error.message);
        return false;
    }
}

// Fungsi untuk mendapatkan IP address dari request
function getClientIP(req) {
    return req.headers['x-forwarded-for'] ||
           req.headers['x-real-ip'] ||
           req.connection.remoteAddress ||
           req.socket.remoteAddress ||
           (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
           '127.0.0.1';
}

// Fungsi untuk cek apakah user adalah premium
function isPremiumUser(chatId, ipAddress) {
    const premiumData = readJsonFile('premium.json', { premium_users: [] });

    return premiumData.premium_users.some(user => {
        const isActive = user.status === 'active';
        const notExpired = !user.expires_date || new Date(user.expires_date) > new Date();
        const matchesChatId = chatId && user.chat_id === chatId;
        const matchesIP = ipAddress && user.ip_address === ipAddress;

        return isActive && notExpired && (matchesChatId || matchesIP);
    });
}

// Fungsi untuk mendapatkan usage hari ini
function getTodayUsage(ipAddress) {
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const usageData = readJsonFile('usage.json', { daily_usage: {} });

    if (!usageData.daily_usage[today]) {
        usageData.daily_usage[today] = {};
    }

    if (!usageData.daily_usage[today][ipAddress]) {
        usageData.daily_usage[today][ipAddress] = {
            count: 0,
            first_request: null,
            last_request: null,
            requests: []
        };
    }

    return usageData.daily_usage[today][ipAddress];
}

// Fungsi untuk menambah usage
function addUsage(ipAddress, apiUrl, status) {
    const today = new Date().toISOString().split('T')[0];
    const now = new Date().toISOString();
    const usageData = readJsonFile('usage.json', { daily_usage: {}, settings: {} });

    if (!usageData.daily_usage[today]) {
        usageData.daily_usage[today] = {};
    }

    if (!usageData.daily_usage[today][ipAddress]) {
        usageData.daily_usage[today][ipAddress] = {
            count: 0,
            first_request: null,
            last_request: null,
            requests: []
        };
    }

    const userUsage = usageData.daily_usage[today][ipAddress];
    userUsage.count++;
    userUsage.last_request = now;

    if (!userUsage.first_request) {
        userUsage.first_request = now;
    }

    userUsage.requests.push({
        timestamp: now,
        url: apiUrl,
        status: status
    });

    // Cleanup old data (keep only last 7 days)
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - 7);
    const cutoffDateStr = cutoffDate.toISOString().split('T')[0];

    Object.keys(usageData.daily_usage).forEach(date => {
        if (date < cutoffDateStr) {
            delete usageData.daily_usage[date];
        }
    });

    writeJsonFile('usage.json', usageData);
    return userUsage.count;
}

// Fungsi untuk cek limit user
function checkUserLimit(chatId, ipAddress) {
    const premiumData = readJsonFile('premium.json', { settings: { free_daily_limit: 3 } });
    const freeLimit = premiumData.settings.free_daily_limit || 3;

    // Cek apakah user premium
    if (isPremiumUser(chatId, ipAddress)) {
        return { allowed: true, isPremium: true, remaining: 'unlimited' };
    }

    // Cek usage untuk user gratis
    const todayUsage = getTodayUsage(ipAddress);
    const remaining = Math.max(0, freeLimit - todayUsage.count);

    return {
        allowed: todayUsage.count < freeLimit,
        isPremium: false,
        remaining: remaining,
        used: todayUsage.count,
        limit: freeLimit
    };
}

// Fungsi untuk mengirim pesan ke Telegram
async function sendTelegramAlert(message) {
    const token = process.env.TELEGRAM_BOT_TOKEN;
    const chatId = process.env.TELEGRAM_CHAT_ID;
    
    if (!token || !chatId) {
        console.error('Telegram bot token atau chat ID tidak ditemukan di .env');
        return;
    }
    
    const telegramUrl = `https://api.telegram.org/bot${token}/sendMessage`;
    const payload = JSON.stringify({
        chat_id: chatId,
        text: message
    });
    
    try {
        const response = await fetch(telegramUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: payload
        });
        
        if (response.ok) {
            console.log('Pesan Telegram berhasil dikirim');
        } else {
            console.error('Gagal mengirim pesan Telegram:', response.statusText);
        }
    } catch (error) {
        console.error('Error mengirim pesan Telegram:', error.message);
    }
}

// Fungsi untuk melakukan pengecekan API
async function checkApiEndpoint(apiUrl) {
    try {
        const response = await fetch(apiUrl);
        const status = response.status;
        
        if (status === 200) {
            return { ok: true, status: 200 };
        } else {
            // Kirim alert ke Telegram jika status bukan 200
            const alertMessage = `🚨 API Alert!\nURL: ${apiUrl}\nStatus: ${status}\nTime: ${new Date().toISOString()}`;
            await sendTelegramAlert(alertMessage);
            
            return { ok: false, status: status };
        }
    } catch (error) {
        // Kirim alert ke Telegram jika terjadi error
        const alertMessage = `🚨 API Error!\nURL: ${apiUrl}\nError: ${error.message}\nTime: ${new Date().toISOString()}`;
        await sendTelegramAlert(alertMessage);
        
        return { ok: false, status: 'error', message: error.message };
    }
}

// Fungsi untuk melayani file statis
function serveStaticFile(filePath, res) {
    fs.readFile(filePath, (err, data) => {
        if (err) {
            res.writeHead(404, { 'Content-Type': 'text/plain' });
            res.end('File not found');
            return;
        }
        
        const ext = path.extname(filePath);
        let contentType = 'text/plain';
        
        switch (ext) {
            case '.html':
                contentType = 'text/html';
                break;
            case '.css':
                contentType = 'text/css';
                break;
            case '.js':
                contentType = 'application/javascript';
                break;
            case '.json':
                contentType = 'application/json';
                break;
        }
        
        res.writeHead(200, { 'Content-Type': contentType });
        res.end(data);
    });
}

// Membuat server HTTP
const server = http.createServer(async (req, res) => {
    const parsedUrl = url.parse(req.url, true);
    const pathname = parsedUrl.pathname;
    const query = parsedUrl.query;
    
    // CORS headers untuk development
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }
    
    // Route untuk halaman utama
    if (pathname === '/' && req.method === 'GET') {
        serveStaticFile(path.join(__dirname, 'public', 'index.html'), res);
        return;
    }

    // Route untuk halaman upgrade
    if (pathname === '/upgrade.html' && req.method === 'GET') {
        serveStaticFile(path.join(__dirname, 'public', 'upgrade.html'), res);
        return;
    }
    
    // Route untuk pengecekan API
    if (pathname === '/check' && req.method === 'GET') {
        const apiUrl = query.url;
        const chatId = query.chat_id; // Optional chat_id parameter
        const clientIP = getClientIP(req);

        if (!apiUrl) {
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Parameter URL diperlukan' }));
            return;
        }

        // Cek limit user
        const limitCheck = checkUserLimit(chatId, clientIP);

        if (!limitCheck.allowed) {
            // Redirect ke halaman upgrade jika limit tercapai
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                error: 'limit_exceeded',
                message: 'Batas harian tercapai. Upgrade ke premium untuk akses unlimited.',
                used: limitCheck.used,
                limit: limitCheck.limit,
                remaining: limitCheck.remaining,
                upgrade_url: '/upgrade.html',
                isPremium: false
            }));
            return;
        }

        try {
            const result = await checkApiEndpoint(apiUrl);

            // Tambah usage setelah request berhasil
            const newCount = addUsage(clientIP, apiUrl, result.status || result.ok ? 200 : 500);

            // Tambahkan info limit ke response
            const updatedLimitCheck = checkUserLimit(chatId, clientIP);
            result.usage_info = {
                isPremium: updatedLimitCheck.isPremium,
                used: updatedLimitCheck.isPremium ? 'unlimited' : newCount,
                remaining: updatedLimitCheck.remaining,
                limit: updatedLimitCheck.limit
            };

            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify(result));
        } catch (error) {
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Internal server error' }));
        }
        return;
    }

    // Route untuk cek status premium
    if (pathname === '/status' && req.method === 'GET') {
        const chatId = query.chat_id;
        const clientIP = getClientIP(req);
        const limitCheck = checkUserLimit(chatId, clientIP);

        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            ip_address: clientIP,
            chat_id: chatId || null,
            isPremium: limitCheck.isPremium,
            used: limitCheck.used || 0,
            remaining: limitCheck.remaining,
            limit: limitCheck.limit || 3,
            allowed: limitCheck.allowed
        }));
        return;
    }

    // Route untuk melayani file statis dari folder public
    if (pathname.startsWith('/public/')) {
        const filePath = path.join(__dirname, pathname);
        serveStaticFile(filePath, res);
        return;
    }
    
    // 404 untuk route yang tidak ditemukan
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Route not found');
});

// Menjalankan server
server.listen(PORT, () => {
    console.log(`🚀 Server berjalan di http://localhost:${PORT}`);
    console.log(`📊 API Monitor + Alert siap digunakan!`);
});
