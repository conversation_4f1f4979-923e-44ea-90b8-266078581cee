# 🔍 Public API Monitor + Alert

Micro SaaS sederhana untuk monitoring endpoint API dengan notifikasi Telegram menggunakan Node.js tanpa framework.

## ✨ Fitur

- ✅ Monitor status endpoint API
- 📱 Alert otomatis ke Telegram jika status ≠ 200
- 🌐 Interface web sederhana untuk testing
- 📊 <PERSON>an dan cepat (tanpa database)
- 🚀 Mudah di-deploy

## 📁 Struktur Project

```
/project-folder/
├── server.js               ← Server HTTP utama
├── public/
│   └── index.html          ← Interface web
├── endpoints.json          ← Daftar endpoint (dummy)
├── .env                    ← Konfigurasi Telegram
├── package.json            ← Dependencies
└── README.md               ← Dokumentasi
```

## 🚀 Cara Menjalankan

### 1. Install Dependencies

```bash
npm install
```

### 2. Setup Telegram Bot

1. Buat bot baru di Telegram dengan mengirim `/newbot` ke [@BotFather](https://t.me/BotFather)
2. Dapatkan token bot dari <PERSON>tFather
3. Dapatkan Chat ID dengan cara:
   - <PERSON><PERSON> ke bot Anda
   - Akses: `https://api.telegram.org/bot<TOKEN>/getUpdates`
   - Cari `chat.id` di response JSON

### 3. Konfigurasi Environment

Edit file `.env`:

```env
TELEGRAM_BOT_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz
TELEGRAM_CHAT_ID=123456789
PORT=3000
```

### 4. Jalankan Server

```bash
npm start
```

Server akan berjalan di: http://localhost:3000

## 📖 Cara Menggunakan

### Via Web Interface

1. Buka http://localhost:3000
2. Masukkan URL endpoint API yang ingin dimonitor
3. Klik "Cek Status API"
4. Lihat hasil pengecekan

### Via API Endpoint

```bash
# Cek status API
curl "http://localhost:3000/check?url=https://api.example.com"
```

Response jika OK:
```json
{
  "ok": true,
  "status": 200
}
```

Response jika Error:
```json
{
  "ok": false,
  "status": 500
}
```

## 🧪 URL Testing

Gunakan URL berikut untuk testing:

- ✅ **Status 200**: https://httpstat.us/200
- ❌ **Status 500**: https://httpstat.us/500
- 📊 **JSON API**: https://jsonplaceholder.typicode.com/posts/1
- 🐙 **GitHub API**: https://api.github.com

## 🔧 Kustomisasi

### Mengubah Port

Edit file `.env`:
```env
PORT=8080
```

### Menambah Endpoint ke Monitoring

Edit file `endpoints.json` untuk menambah endpoint yang akan dimonitor secara otomatis (fitur future).

## 📱 Format Pesan Telegram

Ketika API error, bot akan mengirim pesan seperti:

```
🚨 API Alert!
URL: https://api.example.com
Status: 500
Time: 2024-01-15T10:30:00.000Z
```

## 🛠️ Troubleshooting

### Server tidak bisa start
- Pastikan port 3000 tidak digunakan aplikasi lain
- Cek file `.env` sudah dikonfigurasi dengan benar

### Telegram alert tidak terkirim
- Pastikan `TELEGRAM_BOT_TOKEN` dan `TELEGRAM_CHAT_ID` benar
- Cek koneksi internet
- Pastikan bot sudah di-start dengan `/start`

### Error "fetch is not defined"
- Pastikan menggunakan Node.js versi 18+ atau install polyfill

## 📋 Requirements

- Node.js 14+
- NPM atau Yarn
- Koneksi internet
- Telegram Bot Token

## 🚀 Deployment

### Vercel (Recommended)

1. Push code ke GitHub
2. Connect repository ke Vercel
3. Set environment variables di Vercel dashboard
4. Deploy!

### Railway

1. Connect GitHub repository
2. Set environment variables
3. Deploy dengan satu klik

### VPS/Server

```bash
# Clone repository
git clone <your-repo>
cd public-api-monitor-alert

# Install dependencies
npm install

# Set environment variables
cp .env.example .env
# Edit .env dengan token yang benar

# Install PM2 untuk production
npm install -g pm2

# Start dengan PM2
pm2 start server.js --name "api-monitor"
pm2 startup
pm2 save
```

## 📄 License

MIT License - Silakan gunakan untuk project komersial atau personal.

## 🤝 Contributing

Pull requests welcome! Untuk perubahan besar, silakan buat issue terlebih dahulu.

---

**Happy Monitoring! 🚀**
