{"endpoints": [{"id": 1, "name": "GitHub API", "url": "https://api.github.com", "description": "GitHub REST API endpoint", "expected_status": 200, "check_interval": 300, "active": true, "created_at": "2024-01-01T00:00:00Z"}, {"id": 2, "name": "JSONPlaceholder", "url": "https://jsonplaceholder.typicode.com/posts/1", "description": "Fake REST API for testing", "expected_status": 200, "check_interval": 600, "active": true, "created_at": "2024-01-01T00:00:00Z"}, {"id": 3, "name": "HTTPStat 200", "url": "https://httpstat.us/200", "description": "Test endpoint that returns 200", "expected_status": 200, "check_interval": 300, "active": true, "created_at": "2024-01-01T00:00:00Z"}, {"id": 4, "name": "HTTPStat 500", "url": "https://httpstat.us/500", "description": "Test endpoint that returns 500 (for testing alerts)", "expected_status": 200, "check_interval": 300, "active": false, "created_at": "2024-01-01T00:00:00Z"}], "settings": {"default_check_interval": 300, "telegram_enabled": true, "max_retries": 3, "timeout": 10000}}