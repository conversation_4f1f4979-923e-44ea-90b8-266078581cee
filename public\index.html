<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Public API Monitor + Alert</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 1.1em;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
        }
        
        input[type="url"] {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        input[type="url"]:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: transform 0.2s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .result {
            margin-top: 25px;
            padding: 20px;
            border-radius: 8px;
            display: none;
        }
        
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .example {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .example h3 {
            color: #495057;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .example ul {
            list-style: none;
            padding-left: 0;
        }
        
        .example li {
            color: #6c757d;
            margin-bottom: 5px;
            padding-left: 20px;
            position: relative;
        }
        
        .example li:before {
            content: "•";
            color: #667eea;
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        .status-bar {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 25px;
            text-align: center;
        }

        .status-bar.premium {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-color: #c3e6cb;
            color: #155724;
        }

        .status-bar.warning {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-color: #ffeaa7;
            color: #856404;
        }

        .status-bar.danger {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border-color: #f5c6cb;
            color: #721c24;
        }

        .status-text {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .status-details {
            font-size: 0.9em;
            opacity: 0.8;
        }

        .upgrade-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }

        .upgrade-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 API Monitor</h1>
            <p>Monitor status endpoint API dan dapatkan alert via Telegram</p>
        </div>

        <div class="status-bar" id="statusBar" style="display: none;">
            <div class="status-text" id="statusText">Loading...</div>
            <div class="status-details" id="statusDetails">Checking your account status...</div>
        </div>

        <form id="apiForm">
            <div class="form-group">
                <label for="apiUrl">URL Endpoint API:</label>
                <input 
                    type="url" 
                    id="apiUrl" 
                    name="apiUrl" 
                    placeholder="https://api.example.com/health"
                    required
                >
            </div>
            
            <button type="submit" class="btn" id="checkBtn">
                🚀 Cek Status API
            </button>
        </form>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Sedang mengecek endpoint...</p>
        </div>
        
        <div class="result" id="result"></div>
        
        <div class="example">
            <h3>📝 Contoh URL untuk testing:</h3>
            <ul>
                <li>https://httpstat.us/200 (Status OK)</li>
                <li>https://httpstat.us/500 (Status Error)</li>
                <li>https://jsonplaceholder.typicode.com/posts/1 (JSON API)</li>
                <li>https://api.github.com (GitHub API)</li>
            </ul>
        </div>
    </div>

    <script>
        const form = document.getElementById('apiForm');
        const loading = document.getElementById('loading');
        const result = document.getElementById('result');
        const checkBtn = document.getElementById('checkBtn');
        const statusBar = document.getElementById('statusBar');
        const statusText = document.getElementById('statusText');
        const statusDetails = document.getElementById('statusDetails');

        // Load status on page load
        async function loadStatus() {
            try {
                const response = await fetch('/status');
                const status = await response.json();

                statusBar.style.display = 'block';

                if (status.isPremium) {
                    statusBar.className = 'status-bar premium';
                    statusText.textContent = '⭐ Premium Account';
                    statusDetails.textContent = 'Unlimited API checks available';
                } else {
                    const remaining = status.remaining;
                    if (remaining > 1) {
                        statusBar.className = 'status-bar';
                        statusText.textContent = `🆓 Free Account - ${remaining} checks remaining today`;
                        statusDetails.textContent = `You've used ${status.used}/${status.limit} free checks today`;
                    } else if (remaining === 1) {
                        statusBar.className = 'status-bar warning';
                        statusText.textContent = '⚠️ Last free check remaining today';
                        statusDetails.innerHTML = `You've used ${status.used}/${status.limit} free checks. <a href="/upgrade.html" class="upgrade-link">Upgrade to Premium</a>`;
                    } else {
                        statusBar.className = 'status-bar danger';
                        statusText.textContent = '🚫 Daily limit reached';
                        statusDetails.innerHTML = `You've used all ${status.limit} free checks today. <a href="/upgrade.html" class="upgrade-link">Upgrade to Premium</a>`;
                        checkBtn.disabled = true;
                        checkBtn.textContent = '🔒 Limit Reached - Upgrade Required';
                    }
                }
            } catch (error) {
                console.error('Error loading status:', error);
                statusBar.style.display = 'none';
            }
        }

        // Load status when page loads
        loadStatus();

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const apiUrl = document.getElementById('apiUrl').value;
            
            // Show loading
            loading.style.display = 'block';
            result.style.display = 'none';
            checkBtn.disabled = true;
            checkBtn.textContent = '⏳ Mengecek...';
            
            try {
                const response = await fetch(`/check?url=${encodeURIComponent(apiUrl)}`);
                const data = await response.json();
                
                // Hide loading
                loading.style.display = 'none';
                checkBtn.disabled = false;
                checkBtn.textContent = '🚀 Cek Status API';
                
                // Check if limit exceeded
                if (data.error === 'limit_exceeded') {
                    // Redirect to upgrade page
                    window.location.href = '/upgrade.html';
                    return;
                }

                // Show result
                result.style.display = 'block';

                if (data.ok) {
                    result.className = 'result success';
                    result.innerHTML = `
                        <h3>✅ API Status: OK</h3>
                        <p><strong>URL:</strong> ${apiUrl}</p>
                        <p><strong>Status Code:</strong> ${data.status}</p>
                        <p><strong>Waktu Cek:</strong> ${new Date().toLocaleString('id-ID')}</p>
                        ${data.usage_info ? `<p><strong>Usage:</strong> ${data.usage_info.isPremium ? 'Unlimited (Premium)' : `${data.usage_info.used}/${data.usage_info.limit} today`}</p>` : ''}
                    `;
                } else {
                    result.className = 'result error';
                    result.innerHTML = `
                        <h3>❌ API Status: Error</h3>
                        <p><strong>URL:</strong> ${apiUrl}</p>
                        <p><strong>Status Code:</strong> ${data.status}</p>
                        <p><strong>Waktu Cek:</strong> ${new Date().toLocaleString('id-ID')}</p>
                        <p><strong>Alert:</strong> Notifikasi telah dikirim ke Telegram 📱</p>
                        ${data.message ? `<p><strong>Error:</strong> ${data.message}</p>` : ''}
                        ${data.usage_info ? `<p><strong>Usage:</strong> ${data.usage_info.isPremium ? 'Unlimited (Premium)' : `${data.usage_info.used}/${data.usage_info.limit} today`}</p>` : ''}
                    `;
                }

                // Reload status after successful check
                loadStatus();
            } catch (error) {
                // Hide loading
                loading.style.display = 'none';
                checkBtn.disabled = false;
                checkBtn.textContent = '🚀 Cek Status API';
                
                // Show error
                result.style.display = 'block';
                result.className = 'result error';
                result.innerHTML = `
                    <h3>❌ Terjadi Kesalahan</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                    <p>Silakan coba lagi atau periksa URL yang dimasukkan.</p>
                `;
            }
        });
    </script>
</body>
</html>
