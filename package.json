{"name": "public-api-monitor-alert", "version": "1.0.0", "description": "Micro SaaS untuk monitoring API endpoint dengan alert Telegram", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "test": "echo \"No tests specified\" && exit 0"}, "keywords": ["api", "monitor", "alert", "telegram", "nodejs", "micro-saas"], "author": "Your Name", "license": "MIT", "dependencies": {"dotenv": "^16.3.1", "node-fetch": "^2.7.0"}, "engines": {"node": ">=14.0.0"}}